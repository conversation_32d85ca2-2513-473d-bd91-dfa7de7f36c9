#!/usr/bin/env python3
"""
Enhancement script for Frontend-Backend integration.

This script adds the missing critical integrations to the Chainlit frontend:
1. /query/image endpoint integration (OCR + retrieval)
2. Custom prompt support for image analysis
3. Enhanced error handling
4. Document ingestion interface
"""

import os
import sys
import shutil
from datetime import datetime

def backup_file(file_path):
    """Create a backup of the original file."""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"✓ Created backup: {backup_path}")
    return backup_path

def enhance_chainlit_app():
    """Add missing integrations to chainlit_app.py"""
    app_file = "chainlit_app.py"
    
    if not os.path.exists(app_file):
        print(f"❌ {app_file} not found")
        return False
    
    # Create backup
    backup_path = backup_file(app_file)
    
    try:
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Enhancement 1: Add query_image_with_ocr method to RAGClient class
        ocr_method = '''
    async def query_image_with_ocr(self, image_bytes: bytes, retrieval_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Upload an image to extract text via OCR and run retrieval queries.
        """
        url = f"{self.base_url}/query/image"
        data = aiohttp.FormData()
        data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')
        
        if retrieval_config:
            data.add_field('retrieval_config', json.dumps(retrieval_config))
        
        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, data=data) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Image OCR query failed: {error_text}")

    async def ingest_documents(self) -> Dict[str, Any]:
        """Trigger document ingestion"""
        url = f"{self.base_url}/ingest"
        headers = {"Content-Type": "application/json"}

        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Document ingestion failed: {error_text}")'''
        
        # Find the end of RAGClient class and add the new methods
        class_end_pattern = "# Initialize client\nrag_client = RAGClient(API_BASE_URL)"
        if class_end_pattern in content:
            content = content.replace(class_end_pattern, ocr_method + "\n\n" + class_end_pattern)
            print("✓ Added OCR and ingestion methods to RAGClient")
        
        # Enhancement 2: Add /ocr command to the command handler
        ocr_command = '''        elif user_input == "/ocr":
            # Show image upload prompt for OCR + retrieval
            await cl.Message(
                content="📄 **OCR + Retrieval**\\n\\nUpload an image to extract text and search your documents.",
                author="Assistant"
            ).send()
            
            files = await cl.AskFileMessage(
                content="Upload an image file for text extraction",
                accept=["image/png", "image/jpeg", "image/jpg"],
                max_size_mb=10,
                timeout=180,
            ).send()
            
            if not files:
                await cl.Message(
                    content="❌ No file was uploaded or the upload timed out.",
                    author="Assistant"
                ).send()
                return
                
            file = files[0]
            loading_msg = await cl.Message(
                content="🔍 Extracting text and searching documents...",
                author="Assistant"
            ).send()
            
            try:
                # Show uploaded image
                temp_dir = os.path.join(os.getcwd(), "temp_images")
                os.makedirs(temp_dir, exist_ok=True)
                temp_file = os.path.join(temp_dir, f"ocr_{file.name}")
                
                with open(temp_file, "wb") as f:
                    f.write(file.content)
                
                image_element = cl.Image(path=temp_file, name="ocr_image")
                await cl.Message(
                    content=f"**Processing Image**: {file.name}",
                    elements=[image_element]
                ).send()
                
                # Process with OCR + retrieval
                result = await rag_client.query_image_with_ocr(file.content)
                response_content = format_query_response(result)
                
                # Add extracted text section
                extracted_text = result.get("extracted_text", "").strip()
                if extracted_text:
                    response_content += f"\\n\\n## 📝 **Extracted Text**\\n\\n```\\n{extracted_text[:500]}\\n```"
                    if len(extracted_text) > 500:
                        response_content += "\\n*(text truncated)*"
                
                loading_msg.content = response_content
                await loading_msg.update()
                
                # Show sources if available
                if result.get("sources"):
                    sources_msg = format_sources(result["sources"])
                    await cl.Message(content=sources_msg, author="Sources").send()
                    
            except Exception as e:
                loading_msg.content = f"❌ **OCR Query Failed**\\n\\nError: {str(e)}"
                await loading_msg.update()
                
        elif user_input == "/ingest":
            # Document ingestion command
            loading_msg = await cl.Message(
                content="📚 Starting document ingestion from S3...",
                author="Assistant"
            ).send()
            
            try:
                result = await rag_client.ingest_documents()
                
                status = result.get("status", "unknown")
                message = result.get("message", "No message provided")
                chunks = result.get("chunks", "unknown")
                
                if status == "success":
                    loading_msg.content = f"✅ **Ingestion Completed**\\n\\n{message}\\n\\n**Documents processed**: {chunks} chunks"
                else:
                    loading_msg.content = f"⚠️ **Ingestion Status**: {status}\\n\\n{message}"
                    
                await loading_msg.update()
                
            except Exception as e:
                loading_msg.content = f"❌ **Ingestion Failed**\\n\\nError: {str(e)}"
                await loading_msg.update()
                
        '''
        
        # Find the /analyze command and add the new commands before it
        analyze_pattern = '        elif user_input == "/analyze":'
        if analyze_pattern in content:
            content = content.replace(analyze_pattern, ocr_command + analyze_pattern)
            print("✓ Added /ocr and /ingest commands")
        
        # Enhancement 3: Update help message to include new commands
        old_help = '''## **Available Commands:**
- **`/help`** - Show this help message
- **`/diagrams`** - Show architecture diagrams from your documents
- **`/tokens`** - Show session token usage summary'''
        
        new_help = '''## **Available Commands:**
- **`/help`** - Show this help message
- **`/diagrams`** - Show architecture diagrams from your documents
- **`/tokens`** - Show session token usage summary
- **`/ocr`** - Upload image for text extraction and document search
- **`/ingest`** - Trigger document ingestion from S3
- **`/analyze`** - Analyze uploaded image with AI vision models'''
        
        if old_help in content:
            content = content.replace(old_help, new_help)
            print("✓ Updated help message with new commands")
        
        # Enhancement 4: Update welcome message
        old_welcome = '''## 📚 **Available Commands:**
- **`/help`** - Show help message
- **`/diagrams`** - Show architecture diagrams from your documents
- **`/tokens`** - Show session token usage summary
- **`/analyze`** - Analyze an uploaded image (architecture diagram or error screenshot)'''
        
        new_welcome = '''## 📚 **Available Commands:**
- **`/help`** - Show help message
- **`/diagrams`** - Show architecture diagrams from your documents
- **`/tokens`** - Show session token usage summary
- **`/ocr`** - Extract text from images and search documents
- **`/ingest`** - Trigger document ingestion from S3
- **`/analyze`** - Analyze uploaded images with AI vision models'''
        
        if old_welcome in content:
            content = content.replace(old_welcome, new_welcome)
            print("✓ Updated welcome message with new commands")
        
        # Enhancement 5: Update analyze_image_with_ai function for custom prompts
        old_analyze_function = '''async def analyze_image_with_ai(image_bytes: bytes, use_openrouter: bool = False) -> Dict[str, Any]:'''
        new_analyze_function = '''async def analyze_image_with_ai(image_bytes: bytes, use_openrouter: bool = False, custom_prompt: Optional[str] = None) -> Dict[str, Any]:'''
        
        if old_analyze_function in content:
            content = content.replace(old_analyze_function, new_analyze_function)
            
            # Also update the FormData construction
            old_formdata = '''    data = aiohttp.FormData()
    data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')'''
            
            new_formdata = '''    data = aiohttp.FormData()
    data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')
    
    # Add custom prompt for OpenRouter
    if use_openrouter and custom_prompt:
        data.add_field('prompt', custom_prompt)'''
        
            if old_formdata in content:
                content = content.replace(old_formdata, new_formdata)
                print("✓ Added custom prompt support to image analysis")
        
        # Write the enhanced content back
        with open(app_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Successfully enhanced {app_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error enhancing file: {e}")
        # Restore backup
        shutil.copy2(backup_path, app_file)
        print(f"🔄 Restored backup from {backup_path}")
        return False

def main():
    """Main function to apply all enhancements."""
    print("=" * 60)
    print("FRONTEND-BACKEND INTEGRATION ENHANCEMENT")
    print("=" * 60)
    
    # Change to Frontend directory if not already there
    if not os.path.exists("chainlit_app.py"):
        if os.path.exists("Frontend/chainlit_app.py"):
            os.chdir("Frontend")
            print("📁 Changed to Frontend directory")
        else:
            print("❌ Could not find chainlit_app.py file")
            return 1
    
    success = True
    
    # Apply enhancements
    print("\n🔧 Enhancing chainlit_app.py...")
    if not enhance_chainlit_app():
        success = False
    
    if success:
        print("\n✅ All enhancements applied successfully!")
        print("\n📋 Summary of changes:")
        print("  • Added /query/image endpoint integration (/ocr command)")
        print("  • Added document ingestion interface (/ingest command)")
        print("  • Added custom prompt support for image analysis")
        print("  • Updated help and welcome messages")
        print("  • Enhanced error handling")
        print("\n🧪 New commands available:")
        print("  • /ocr - Upload image for text extraction and search")
        print("  • /ingest - Trigger document ingestion from S3")
        print("  • /analyze - Enhanced image analysis with custom prompts")
        return 0
    else:
        print("\n❌ Some enhancements failed. Check the error messages above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
