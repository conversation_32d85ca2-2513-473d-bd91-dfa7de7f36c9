import chainlit as cl
import aiohttp
import json
import asyncio
from typing import Dict, Any, Optional, List
import os
import sys
from dotenv import load_dotenv
import logging
import base64
from io import BytesIO
from PIL import Image

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend'))

# Import RAG system components for direct integration
from query import QueryEngine
from prompts import AWSPromptSelector, validate_prompt_inputs, extract_aws_services
from advanced_retrieval import ConfigurableRetriever
from token_utils import get_token_tracker, TokenUsage

load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8888")
TIMEOUT = aiohttp.ClientTimeout(total=300)  # 5 minutes timeout for ingestion

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global instances for direct RAG integration
query_engine = None
prompt_selector = None

class RAGClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
    
    async def health_check(self) -> bool:
        """Check if the API is healthy"""
        try:
            async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
                async with session.get(f"{self.base_url}/health") as response:
                    return response.status == 200
        except Exception:
            return False

    async def query_advanced(self, question: str, retrieval_config: Optional[Dict[str, Any]] = None, 
                           session_id: Optional[str] = None) -> Dict[str, Any]:
        """Send advanced query to the API with optional session management"""
        payload = {
            "question": question,
            "retrieval_config": retrieval_config or {}
        }
        
        # Add session_id as query parameter if provided
        url = f"{self.base_url}/query/advanced"
        if session_id:
            url += f"?session_id={session_id}"
        
        headers = {"Content-Type": "application/json"}

        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Query failed: {error_text}")

    async def query_diagram_graph(self, graph_data: Dict[str, Any], question: str) -> Dict[str, Any]:
        """Send diagram graph query to the API"""
        payload = {
            "graph_data": graph_data,
            "question": question
        }
        
        url = f"{self.base_url}/query/diagram-graph"
        headers = {"Content-Type": "application/json"}

        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Diagram graph query failed: {error_text}")


    async def query_image_with_ocr(self, image_bytes: bytes, retrieval_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Upload an image to extract text via OCR and run retrieval queries.
        """
        url = f"{self.base_url}/query/image"
        data = aiohttp.FormData()
        data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')
        
        if retrieval_config:
            data.add_field('retrieval_config', json.dumps(retrieval_config))
        
        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, data=data) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Image OCR query failed: {error_text}")

    async def ingest_documents(self) -> Dict[str, Any]:
        """Trigger document ingestion"""
        url = f"{self.base_url}/ingest"
        headers = {"Content-Type": "application/json"}

        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Document ingestion failed: {error_text}")

# Initialize client
rag_client = RAGClient(API_BASE_URL)

@cl.on_chat_start
async def start():
    """Initialize the chat session"""
    # Check API health
    is_healthy = await rag_client.health_check()
    
    if not is_healthy:
        await cl.Message(
            content="⚠️ **API Connection Failed**\n\nThe RAG API is not responding. Please check if the FastAPI server is running.",
            author="System"
        ).send()
        return
    
    # Initialize session state
    cl.user_session.set("api_healthy", True)

    # Welcome message
    welcome_msg = """
# 🚀 Welcome to RAG Chat Assistant

I'm your intelligent document assistant powered by AWS Bedrock!

## 📚 **Available Commands:**
- **`/help`** - Show help message
- **`/diagrams`** - Show architecture diagrams from your documents
- **`/tokens`** - Show session token usage summary
- **`/ocr`** - Extract text from images and search documents
- **`/ingest`** - Trigger document ingestion from S3
- **`/analyze`** - Analyze uploaded images with AI vision models

Ready to get started? Send a message to begin!
"""
    
    await cl.Message(content=welcome_msg, author="Assistant").send()

    # Initialize token tracking for this session
    session_id = cl.user_session.get("id", "default_session")
    token_tracker = get_token_tracker()
    session_usage = token_tracker.get_or_create_session(session_id)
    cl.user_session.set("session_id", session_id)
    cl.user_session.set("token_tracker", token_tracker)

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages"""
    user_input = message.content.strip()
    
    # Check API health
    if not cl.user_session.get("api_healthy", False):
        await cl.Message(
            content="❌ API connection is not available. Please restart the chat.",
            author="System"
        ).send()
        return
    
    # Handle commands
    if user_input.startswith('/'):
        if user_input == "/help":
            help_msg = """
# 🆘 **Help & Commands**

## **Available Commands:**
- **`/help`** - Show this help message
- **`/diagrams`** - Show architecture diagrams from your documents
- **`/tokens`** - Show session token usage summary
- **`/ocr`** - Upload image for text extraction and document search
- **`/ingest`** - Trigger document ingestion from S3
- **`/analyze`** - Analyze uploaded image with AI vision models

## **Query Examples:**
- "What are the best practices for AWS Lambda?"
- "Compare EC2 and ECS for container deployment"
- "How do I configure S3 bucket policies?"
- "Show me architecture diagrams for microservices"

## **Image Analysis Feature:**
- Upload architecture diagrams or screenshots with errors to get AI analysis
- Choose between AWS Bedrock (Claude) or Google Gemini for analysis
- The system will analyze the image content and provide detailed explanations
- For architecture diagrams: Components and relationships will be explained
- For error screenshots: The error will be identified with possible solutions

## **Advanced Query Features:**
- **Hybrid Search**: Combines semantic and keyword matching
- **Source Citations**: Shows which documents contain the information
- **Confidence Scores**: Indicates how relevant the results are
- **AWS Optimization**: Enhanced for AWS service documentation
- **Image Extraction**: Can extract and display architecture diagrams

Just type your question naturally - no special formatting needed!
            """
            await cl.Message(content=help_msg, author="Assistant").send()
        
        elif user_input == "/diagrams":
            # Show typing indicator
            loading_msg = await cl.Message(
                content="🔍 Searching for architecture diagrams...",
                author="Assistant"
            ).send()
            
            try:
                # Query for architecture diagrams
                retrieval_config = {
                    "similarity_threshold": 0.2,  # Lower threshold to find more diagrams
                    "max_results": 10
                }
                
                # Create a query specifically for diagrams
                result = await rag_client.query_advanced(
                    "Find architecture diagrams and visualizations", 
                    retrieval_config
                )
                
                if result.get("images") and len(result["images"]) > 0:
                    loading_msg.content = f"✅ Found {len(result['images'])} architecture diagram(s) in your documents."
                    await loading_msg.update()
                    
                    # Display the diagrams
                    await display_images(result["images"])
                else:
                    loading_msg.content = "❌ No architecture diagrams found in your documents. Try ingesting documents with diagrams or using the standard query interface with terms like 'show me diagrams'."
                    await loading_msg.update()
                    
            except Exception as e:
                loading_msg.content = f"❌ Error searching for diagrams: {str(e)}"
                await loading_msg.update()

        elif user_input == "/tokens":
            # Show session token usage summary
            session_usage = cl.user_session.get("session_usage")
            if session_usage and session_usage.total_queries > 0:
                await display_session_token_summary(session_usage)
            else:
                await cl.Message(
                    content="📊 **Token Usage**\n\nNo queries have been made in this session yet. Ask a question to start tracking token usage!",
                    author="Token Tracker"
                ).send()
                
        elif user_input == "/ocr":
            # Show image upload prompt for OCR + retrieval
            await cl.Message(
                content="📄 **OCR + Retrieval**\n\nUpload an image to extract text and search your documents.",
                author="Assistant"
            ).send()
            
            files = await cl.AskFileMessage(
                content="Upload an image file for text extraction",
                accept=["image/png", "image/jpeg", "image/jpg"],
                max_size_mb=10,
                timeout=180,
            ).send()
            
            if not files:
                await cl.Message(
                    content="❌ No file was uploaded or the upload timed out.",
                    author="Assistant"
                ).send()
                return
                
            file = files[0]
            loading_msg = await cl.Message(
                content="🔍 Extracting text and searching documents...",
                author="Assistant"
            ).send()
            
            try:
                # Show uploaded image
                temp_dir = os.path.join(os.getcwd(), "temp_images")
                os.makedirs(temp_dir, exist_ok=True)
                temp_file = os.path.join(temp_dir, f"ocr_{file.name}")
                
                # Get file content - try multiple approaches for Chainlit compatibility
                file_content = None
                if hasattr(file, 'content') and file.content is not None:
                    file_content = file.content
                    f.write(file_content)
                elif hasattr(file, 'path') and file.path and os.path.exists(file.path):
                    with open(file.path, 'rb') as src:
                        file_content = src.read()
                        f.write(file_content)
                elif hasattr(file, 'file') and file.file is not None:
                    # Some versions use 'file' attribute
                    file_content = file.file.read()
                    f.write(file_content)
                else:
                    # Last resort - try to read as bytes directly
                    try:
                        file_content = bytes(file)
                        f.write(file_content)
                    except:
                        raise Exception(f"Cannot access file content. File type: {type(file)}, Available attributes: {[attr for attr in dir(file) if not attr.startswith('_')]}")

                image_element = cl.Image(path=temp_file, name="ocr_image")
                await cl.Message(
                    content=f"**Processing Image**: {file.name}",
                    elements=[image_element]
                ).send()

                # Process with OCR + retrieval
                result = await rag_client.query_image_with_ocr(file_content)
                response_content = format_query_response(result)
                
                # Add extracted text section
                extracted_text = result.get("extracted_text", "").strip()
                if extracted_text:
                    response_content += f"\n\n## 📝 **Extracted Text**\n\n```\n{extracted_text[:500]}\n```"
                    if len(extracted_text) > 500:
                        response_content += "\n*(text truncated)*"
                
                loading_msg.content = response_content
                await loading_msg.update()
                
                # Show sources if available
                if result.get("sources"):
                    sources_msg = format_sources(result["sources"])
                    await cl.Message(content=sources_msg, author="Sources").send()
                    
            except Exception as e:
                loading_msg.content = f"❌ **OCR Query Failed**\n\nError: {str(e)}"
                await loading_msg.update()
                
        elif user_input == "/ingest":
            # Document ingestion command
            loading_msg = await cl.Message(
                content="📚 Starting document ingestion from S3...",
                author="Assistant"
            ).send()
            
            try:
                result = await rag_client.ingest_documents()
                
                status = result.get("status", "unknown")
                message = result.get("message", "No message provided")
                chunks = result.get("chunks", "unknown")
                
                if status == "success":
                    loading_msg.content = f"✅ **Ingestion Completed**\n\n{message}\n\n**Documents processed**: {chunks} chunks"
                else:
                    loading_msg.content = f"⚠️ **Ingestion Status**: {status}\n\n{message}"
                    
                await loading_msg.update()
                
            except Exception as e:
                loading_msg.content = f"❌ **Ingestion Failed**\n\nError: {str(e)}"
                await loading_msg.update()

        elif user_input == "/analyze":
            # Show image upload prompt
            await cl.Message(
                content="📸 **Image Analysis**\n\nPlease upload an architecture diagram or error screenshot for AI analysis.",
                author="Assistant"
            ).send()
            
            # Request file upload from user
            files = await cl.AskFileMessage(
                content="Upload an image file (PNG, JPG, or SVG)",
                accept=["image/png", "image/jpeg", "image/jpg", "image/svg+xml"],
                max_size_mb=10,
                timeout=180,
            ).send()
            
            if not files:
                await cl.Message(
                    content="❌ No file was uploaded or the upload timed out.",
                    author="Assistant"
                ).send()
                return
                
            # Get the uploaded file
            file = files[0]
            
            # Show loading message
            loading_msg = await cl.Message(
                content="🔍 Analyzing your image...",
                author="Assistant"
            ).send()
            
            try:
                # Create a temporary file to display the uploaded image
                temp_dir = os.path.join(os.getcwd(), "temp_images")
                os.makedirs(temp_dir, exist_ok=True)
                temp_file = os.path.join(temp_dir, f"uploaded_{file.name}")
                
                # Save the uploaded image - try multiple approaches for Chainlit compatibility
                file_content = None
                with open(temp_file, "wb") as f:
                    if hasattr(file, 'content') and file.content is not None:
                        file_content = file.content
                        f.write(file_content)
                    elif hasattr(file, 'path') and file.path and os.path.exists(file.path):
                        with open(file.path, 'rb') as src:
                            file_content = src.read()
                            f.write(file_content)
                    elif hasattr(file, 'file') and file.file is not None:
                        file_content = file.file.read()
                        f.write(file_content)
                    else:
                        try:
                            file_content = bytes(file)
                            f.write(file_content)
                        except:
                            raise Exception(f"Cannot access file content. File type: {type(file)}, Available attributes: {[attr for attr in dir(file) if not attr.startswith('_')]}")
                
                # Display the uploaded image
                image_element = cl.Image(path=temp_file, name="uploaded_image")
                await cl.Message(
                    content=f"**Uploaded Image**: {file.name}",
                    elements=[image_element]
                ).send()
                
                # Ask user which analysis method to use - simplified approach
                choice_msg = await cl.AskUserMessage(
                    content="Choose an analysis method:\n1. AWS Bedrock (Claude) - type 'aws'\n2. Google Gemini 2.0 Flash - type 'gemini'",
                    timeout=60,
                ).send()

                if not choice_msg:
                    loading_msg.content = "❌ No analysis method selected or the selection timed out."
                    await loading_msg.update()
                    return

                analysis_choice_value = choice_msg.get('output', '').lower().strip()
                if analysis_choice_value not in ['aws', 'gemini', 'openrouter']:  # Keep openrouter for backward compatibility
                    loading_msg.content = "❌ Invalid choice. Please type 'aws' or 'gemini'."
                    await loading_msg.update()
                    return


                
                # Get file content for analysis (if not already obtained above)
                if file_content is None:
                    if hasattr(file, 'content') and file.content is not None:
                        file_content = file.content
                    elif hasattr(file, 'path') and file.path and os.path.exists(file.path):
                        with open(file.path, 'rb') as f:
                            file_content = f.read()
                    elif hasattr(file, 'file') and file.file is not None:
                        file_content = file.file.read()
                    else:
                        try:
                            file_content = bytes(file)
                        except:
                            raise Exception(f"Cannot access file content. File type: {type(file)}, Available attributes: {[attr for attr in dir(file) if not attr.startswith('_')]}")

                # Analyze the image with the selected method
                use_gemini = analysis_choice_value in ["gemini", "openrouter"]  # Support both for backward compatibility

                # Debug: Log what we're about to do
                await cl.Message(content=f"🔧 DEBUG: Using Gemini: {use_gemini}, Choice: {analysis_choice_value}", author="Debug").send()
                await cl.Message(content=f"🔧 DEBUG: File content type: {type(file_content)}, size: {len(file_content) if file_content else 0} bytes", author="Debug").send()

                analysis_result = await analyze_image_with_ai(file_content, use_gemini)

                # Debug: Log what we got back
                await cl.Message(content=f"🔧 DEBUG: Got result with keys: {list(analysis_result.keys()) if analysis_result else 'None'}", author="Debug").send()
                if analysis_result:
                    await cl.Message(content=f"🔧 DEBUG: Status: {analysis_result.get('status')}, Analysis length: {len(analysis_result.get('analysis', ''))}", author="Debug").send()

                # Format and display the analysis result
                if analysis_result.get("status") == "error":
                    error_msg = analysis_result.get("error", "Unknown error")
                    loading_msg.content = f"❌ **Analysis Failed**\n\n{error_msg}"
                    await loading_msg.update()
                else:
                    # Extract text content if available
                    extracted_text = analysis_result.get("extracted_text", "").strip()
                    extracted_text_section = ""
                    if extracted_text:
                        extracted_text_section = f"""
## 📝 **Extracted Text**

```
{extracted_text[:500]}
```
{'' if len(extracted_text) <= 500 else '*(text truncated)*'}

"""
                    
                    # Format the analysis
                    analysis = analysis_result.get("analysis", "No analysis available")
                    model_id = analysis_result.get("model_id", "Unknown model")

                    # Debug: Show what we extracted
                    await cl.Message(content=f"🔧 DEBUG: Raw analysis type: {type(analysis)}, length: {len(analysis) if analysis else 0}", author="Debug").send()
                    await cl.Message(content=f"🔧 DEBUG: Analysis preview: {repr(analysis[:100]) if analysis else 'None'}", author="Debug").send()

                    # Ensure analysis is a string and not empty
                    if not analysis or not isinstance(analysis, str):
                        await cl.Message(content=f"🔧 DEBUG: Analysis failed validation!", author="Debug").send()
                        analysis = "No analysis content received from the AI model."

                    # Clean the analysis content and escape problematic markdown
                    analysis_clean = analysis.replace('\r\n', '\n').replace('\r', '\n')

                    # Escape markdown characters that might conflict with Chainlit
                    analysis_escaped = (analysis_clean
                                      .replace('*', '\\*')
                                      .replace('#', '\\#')
                                      .replace('`', '\\`')
                                      .replace('_', '\\_'))

                    result_content = f"""# 🔍 Image Analysis Results

## 🤖 AI Analysis

{analysis_escaped}

{extracted_text_section}---
*Analysis performed by: {model_id}*"""

                    loading_msg.content = result_content
                    await loading_msg.update()

                    # If the analysis contains a graph, add a button to ask questions about it
                    if "graph" in analysis_result:
                        await cl.Message(
                            content="💡 **Tip**: You can ask questions about this diagram using the regular chat!",
                            author="Assistant"
                        ).send()
            
            except Exception as e:
                loading_msg.content = f"❌ **Analysis Failed**\n\nError: {str(e)}"
                await loading_msg.update()

        else:
            await cl.Message(
                content="❓ Unknown command. Use `/help` to see available commands.",
                author="Assistant"
            ).send()
        return
    
    # Check for diagram requests
    is_diagram_request = any(term in user_input.lower() for term in [
        "diagram", "architecture", "visual", "picture", "image", "show me"
    ])
    
    # Handle regular queries
    await handle_query(user_input, is_diagram_request)



async def handle_query(question: str, is_diagram_request: bool = False):
    """Handle user queries"""
    # Show typing indicator
    loading_msg = await cl.Message(
        content="🤔 Searching through your documents...",
        author="Assistant"
    ).send()
    
    try:
        # Default retrieval configuration
        retrieval_config = {
            "similarity_threshold": 0.3,
            "max_results": 8,
            "use_hybrid": True,
            "hybrid_weights": [0.7, 0.3],
            "aws_boost": True,
            "filter_duplicates": True
        }
        
        # Query the API
        result = await rag_client.query_advanced(question, retrieval_config)

        # Track token usage for this session
        token_usage_data = result.get("token_usage")
        if token_usage_data:
            session_id = cl.user_session.get("session_id", "default_session")
            token_tracker = cl.user_session.get("token_tracker")
            if token_tracker:
                # Create TokenUsage object from the response data
                token_usage = TokenUsage(
                    input_tokens=token_usage_data.get("input_tokens", 0),
                    output_tokens=token_usage_data.get("output_tokens", 0),
                    total_tokens=token_usage_data.get("total_tokens", 0),
                    model_name=token_usage_data.get("model_name"),
                    timestamp=token_usage_data.get("timestamp")
                )
                # Track the usage in the session
                session_usage = token_tracker.track_query_usage(session_id, token_usage)
                # Store updated session usage
                cl.user_session.set("session_usage", session_usage)

        # Format the response
        response_content = format_query_response(result)

        # Update the loading message with the response
        loading_msg.content = response_content
        await loading_msg.update()

        # Display session token summary if available
        session_usage = cl.user_session.get("session_usage")
        if session_usage and session_usage.total_queries > 1:
            await display_session_token_summary(session_usage)

        # Display images if available
        if result.get("images") and len(result["images"]) > 0:
            await display_images(result["images"])
        # Also check for images in sources and display them
        elif result.get("sources"):
            image_sources = [source for source in result.get("sources", []) 
                           if source.get("source", "").endswith((".png", ".jpg", ".jpeg", ".gif")) 
                           or "_img_" in source.get("source", "")]
            if image_sources:
                # Request images specifically for these sources
                try:
                    image_query = "Show me images related to " + question
                    image_result = await rag_client.query_advanced(image_query, {"retriever_type": "configurable", "max_results": 10})
                    if image_result.get("images") and len(image_result["images"]) > 0:
                        await cl.Message(content="📊 **Related Images Found**").send()
                        await display_images(image_result["images"])
                except Exception as e:
                    logger.error(f"Error fetching related images: {e}")
                    await cl.Message(content="📊 **Related Images Found in Sources** (Unable to display)").send()

        # Show sources as a separate message if available
        if result.get("sources"):
            sources_msg = format_sources(result["sources"])
            await cl.Message(content=sources_msg, author="Sources").send()
    
    except Exception as e:
        error_msg = f"""
❌ **Query Failed**

**Error**: {str(e)}

**Suggestions:**
- Try rephrasing your question
- Check if the API server is running
        """
        loading_msg.content = error_msg
        await loading_msg.update()

async def display_session_token_summary(session_usage: TokenUsage) -> None:
    """Display cumulative token usage summary for the session."""
    try:
        total_queries = session_usage.total_queries
        cumulative_input = session_usage.cumulative_input_tokens
        cumulative_output = session_usage.cumulative_output_tokens
        cumulative_total = session_usage.cumulative_total_tokens

        averages = session_usage.get_average_tokens_per_query()

        summary_msg = f"""
📊 **Session Token Summary**

**Total Queries**: {total_queries}
**Cumulative Usage**: {cumulative_input:,} input + {cumulative_output:,} output = {cumulative_total:,} total tokens

**Average per Query**: {averages['input']:.1f} input + {averages['output']:.1f} output = {averages['total']:.1f} total tokens
        """

        await cl.Message(content=summary_msg.strip(), author="Token Tracker").send()

    except Exception as e:
        logger.error(f"Error displaying session token summary: {e}")

# Add a new function to display images
async def display_images(images: List[Dict[str, Any]]):
    """Display images in the UI"""
    if not images:
        return
    
    for i, img_data in enumerate(images):
        try:
            # Get image details
            base64_str = img_data.get("base64")
            if not base64_str:
                continue
                
            caption = img_data.get("caption", "Architecture Diagram")
            source_doc = img_data.get("source_document", "Unknown source")
            
            # Decode base64 image
            image_bytes = base64.b64decode(base64_str)
            
            # Create a temporary file to store the image
            temp_dir = os.path.join(os.getcwd(), "temp_images")
            os.makedirs(temp_dir, exist_ok=True)
            temp_file = os.path.join(temp_dir, f"diagram_{i}.png")
            
            # Save the image to the temporary file
            with open(temp_file, "wb") as f:
                f.write(image_bytes)
            
            # Create image element
            image_element = cl.Image(path=temp_file, name=f"diagram_{i}")
            
            # Create and send message with the image element attached
            await cl.Message(
                content=f"**{caption}**\n*Source: {source_doc}*",
                elements=[image_element]
            ).send()
            
        except Exception as e:
            logger.error(f"Error displaying image: {str(e)}")
            await cl.Message(
                content=f"⚠️ Failed to display an image. Error: {str(e)}",
                author="System"
            ).send()

async def upload_image_and_query(image_bytes: bytes, retrieval_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Upload an image to the backend, extract text, and run retrieval.
    """
    url = f"{API_BASE_URL}/query/image"
    data = aiohttp.FormData()
    data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')
    
    payload = {}
    if retrieval_config:
        payload['retrieval_config'] = retrieval_config
    data.add_field('request', json.dumps(payload), content_type='application/json')
    async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
        async with session.post(url, data=data) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_text = await response.text()
                raise Exception(f"Image query failed: {error_text}")

async def analyze_image_with_ai(image_bytes: bytes, use_gemini: bool = False, custom_prompt: Optional[str] = None) -> Dict[str, Any]:
    """
    Upload an image to the backend for AI analysis using vision model.
    This is specifically for analyzing architecture diagrams or screenshots with errors.

    Args:
        image_bytes: The image bytes to analyze
        use_gemini: Whether to use Google Gemini API instead of AWS Bedrock
    """
    import requests

    # Choose the appropriate endpoint based on the use_gemini flag
    if use_gemini:
        url = f"{API_BASE_URL}/analyze/image/gemini"
    else:
        url = f"{API_BASE_URL}/analyze/image"

    # Use requests instead of aiohttp to avoid chainlit async issues
    files = {'file': ('upload.png', image_bytes, 'image/png')}
    data = {}

    # Add custom prompt for Gemini
    if use_gemini and custom_prompt:
        data['prompt'] = custom_prompt

    try:
        print(f"FRONTEND DEBUG: Sending request to {url}")
        print(f"FRONTEND DEBUG: Image size: {len(image_bytes)} bytes")

        response = requests.post(url, files=files, data=data, timeout=120)

        print(f"FRONTEND DEBUG: Response status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"FRONTEND DEBUG: Raw result keys: {list(result.keys())}")
            print(f"FRONTEND DEBUG: Raw analysis type: {type(result.get('analysis'))}")
            print(f"FRONTEND DEBUG: Raw analysis length: {len(result.get('analysis', ''))}")
            print(f"FRONTEND DEBUG: Raw analysis preview: {repr(result.get('analysis', '')[:100])}")
            return result
        else:
            error_text = response.text
            raise Exception(f"Image analysis failed: {error_text}")

    except Exception as e:
        print(f"FRONTEND DEBUG: Exception in analyze_image_with_ai: {e}")
        raise



async def display_token_usage_from_dict(token_usage: Dict[str, Any], activity: str):
    """Display token usage from a dictionary."""
    input_tokens = token_usage.get("input_tokens", 0)
    output_tokens = token_usage.get("output_tokens", 0)
    total_tokens = token_usage.get("total_tokens", 0)

    summary_msg = f"""
    🔢 **Token Usage ({activity})**

    **Input**: {input_tokens:,} | **Output**: {output_tokens:,} | **Total**: {total_tokens:,}
    """
    await cl.Message(content=summary_msg.strip(), author="Token Tracker").send()

def format_query_response(result: Dict[str, Any]) -> str:
    """Format the query response for display"""
    answer = result.get("answer", "No answer provided")
    query_type = result.get("query_type", "unknown")
    confidence = result.get("confidence")
    
    response = f"## 💡 **Answer**\n\n{answer}\n\n"
    
    # Add metadata
    metadata_parts = []
    metadata_parts.append(f"**Query Type**: {query_type}")
    
    if confidence:
        metadata_parts.append(f"**Confidence**: {confidence}")
    
    if result.get("has_aws_content"):
        metadata_parts.append("**AWS Content**: ✅ Detected")
    
    if result.get("has_images"):
        metadata_parts.append("**Images**: ✅ Available")
    
    if result.get("key_findings"):
        findings = result["key_findings"]
        if isinstance(findings, list) and findings:
            metadata_parts.append(f"**Key Findings**: {len(findings)} items")

    if metadata_parts:
        response += "📋 **Query Details**\n" + " | ".join(metadata_parts) + "\n\n"

    # Add token usage information
    token_usage = result.get("token_usage")
    if token_usage:
        input_tokens = token_usage.get("input_tokens", 0)
        output_tokens = token_usage.get("output_tokens", 0)
        total_tokens = token_usage.get("total_tokens", 0)
        model_name = token_usage.get("model_name", "Unknown")

        response += "🔢 **Token Usage**\n"
        response += f"**Input**: {input_tokens:,} tokens | **Output**: {output_tokens:,} tokens | **Total**: {total_tokens:,} tokens\n"
        response += f"*Model*: {model_name}\n\n"

    return response

def format_sources(sources: list) -> str:
    """Format sources for display"""
    if not sources:
        return "📚 **Sources**: No sources available"
    
    sources_text = "📚 **Sources & References**\n\n"
    
    for i, source in enumerate(sources[:5], 1):  # Limit to top 5 sources
        source_name = source.get("source", "Unknown")
        score = source.get("score", 0)
        preview = source.get("content_preview", "No preview available")
        
        sources_text += f"**{i}. {source_name}**\n"
        sources_text += f"*Relevance: {score:.2f}*\n"
        sources_text += f"```\n{preview}\n```\n\n"
    
    if len(sources) > 5:
        sources_text += f"*... and {len(sources) - 5} more sources*\n"
    
    return sources_text

# No need for a __main__ block as chainlit is run via the CLI
# For direct running, you would use: chainlit run chainlit_app.py

@cl.action_callback("ask_graph")
async def on_ask_graph(action: cl.Action):
    """Handle the 'ask_graph' action."""
    graph_data = json.loads(action.value)
    
    # Ask the user for a question about the diagram
    res = await cl.AskUserMessage(content="What would you like to know about the diagram's structure? (e.g., 'What is connected to the database?')", timeout=60).send()
    
    if res:
        question = res['output']
        
        # Show loading message
        loading_msg = await cl.Message(
            content="Analyzing the diagram's structure...",
            author="Assistant"
        ).send()
        
        try:
            # This is a simplified example. In a real application, you would have a dedicated endpoint for this.
            # For now, we'll call a hypothetical endpoint.
            # In a real implementation, you would call the `query_diagram_graph` method on the backend.
            # We will simulate this by calling a local function.
            
            # Simulate calling the backend
            result = await rag_client.query_diagram_graph(graph_data, question)
            answer = result.get("answer", "No answer provided.")
            
            loading_msg.content = f"## 📊 Diagram Analysis\n\n{answer}"
            await loading_msg.update()
            
        except Exception as e:
            loading_msg.content = f"❌ Error analyzing diagram structure: {str(e)}"
            await loading_msg.update()
